# 🧪 خطة اختبار Google Sign-In بعد التحديث

## 📋 **قائمة التحقق قبل الرفع:**

### ✅ **1. إعدادات Firebase:**
- [ ] SHA-1 صحيح: `3ceda50a3b31262b0dc1af26b85ec284da66b467`
- [ ] Client ID صحيح: `545165014521-tk9u5kbu2sva7i4mni5g7h8bc2k5o6ej`
- [ ] Package name: `com.busaty.school`
- [ ] google-services.json محدث

### ✅ **2. اختبار محلي:**
```bash
# تنظيف وبناء
flutter clean
flutter build apk --release

# تشغيل مع logs
flutter run --release
# أو
adb logcat | grep -E "(Google|Sign|Auth|Firebase)"
```

### ✅ **3. خطوات الاختبار:**
1. **فتح التطبيق**
2. **الضغط على "تسجيل دخول بـ Google"**
3. **مراقبة logs للرسائل:**
   - ✅ `"=== Google Sign In Debug ==="`
   - ✅ `"GoogleSignIn instance created"`
   - ✅ `"Using Client ID: ..."`
   - ❌ لا توجد أخطاء

4. **اختيار حساب Google**
5. **التحقق من نجاح تسجيل الدخول**
6. **التحقق من حفظ البيانات:**
   - ✅ `"_saveGoogleSignInFlag - Saved value: true"`

## 🚀 **بعد رفع التحديث:**

### ✅ **1. Play Console:**
- [ ] التحديث تم رفعه بنجاح
- [ ] App signing key لم يتغير
- [ ] SHA-1 fingerprint صحيح

### ✅ **2. اختبار من Play Store:**
1. **تحميل التطبيق من Play Store**
2. **اختبار Google Sign-In**
3. **التحقق من عدم ظهور أخطاء**

### ✅ **3. مراقبة Firebase Analytics:**
- [ ] تسجيل دخول ناجح
- [ ] لا توجد أخطاء authentication

## 🔧 **إذا لم يعمل:**

### **تحقق من:**
1. **Client ID في logs:**
   ```
   "Using Client ID: 545165014521-tk9u5kbu2sva7i4mni5g7h8bc2k5o6ej"
   ```

2. **رسائل الخطأ:**
   ```bash
   adb logcat | grep -E "(Error|Exception|Failed)"
   ```

3. **Firebase Console:**
   - Authentication > Sign-in method > Google
   - Project settings > General > SHA certificate fingerprints

### **حلول سريعة:**
```bash
# إعادة تنظيف شاملة
flutter clean
cd android && ./gradlew clean && cd ..
flutter pub get
flutter build apk --release
```

## 📱 **اختبار نهائي:**
- [ ] تسجيل دخول ناجح
- [ ] حفظ بيانات المستخدم
- [ ] الانتقال للشاشة الرئيسية
- [ ] عدم ظهور أخطاء في logs
