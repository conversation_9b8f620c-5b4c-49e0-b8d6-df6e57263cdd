# تحديثات Google Sign-In لمشروع SchoolX

## ملخص المشكلة
كان تسجيل الدخول بـ Google يعمل في وضع التطوير (Debug) ووضع استخراج APK، لكنه لا يعمل عند رفع التطبيق على Play Store.

## السبب
المشكلة كانت في استخدام Client ID خاطئ للـ Play Store. كان المشروع يستخدم:
- `545165014521-6p1t0u81uhnovk45ogu4s7m060ckg0v5.apps.googleusercontent.com`

بدلاً من Client ID الصحيح المربوط بإعدادات Play Store:
- `545165014521-tk9u5kbu2sva7i4mni5g7h8bc2k5o6ej.apps.googleusercontent.com`

## التغييرات المطبقة

### 1. تحديث android/app/google-services.json
- إضافة Client ID الصحيح للـ Play Store مع SHA-1 fingerprint الصحيح
- Client ID: `545165014521-tk9u5kbu2sva7i4mni5g7h8bc2k5o6ej.apps.googleusercontent.com`
- SHA-1: `3ced5a0a38312628bdc1af26b85ec284da66b467`

### 2. تحديث android/app/build.gradle
- تحديث `REVERSED_CLIENT_ID` ليستخدم Client ID الصحيح:
```gradle
manifestPlaceholders += [
    'REVERSED_CLIENT_ID': 'com.googleusercontent.apps.545165014521-tk9u5kbu2sva7i4mni5g7h8bc2k5o6ej'
]
```

### 3. تحديث lib/config/google_signin_config.dart
- تحديث `androidReleaseClientId` و `androidPlayStoreClientId` ليستخدما Client ID الصحيح
- تحديث تعليق SHA-1 للـ Play Store

### 4. تحديث ios/Runner/GoogleService-Info.plist
- تحديث `ANDROID_CLIENT_ID` ليستخدم Client ID الصحيح للـ Android

## معلومات مهمة

### SHA-1 Fingerprints المستخدمة:
- **Debug**: `8A:C7:3C:0C:FF:C2:1C:28:03:84:97:FB:8E:33:F3:7B:1D:55:D5:23`
- **Release**: `A8:1E:FC:57:F5:B0:31:19:96:39:60:40:BD:FE:A3:7C:CD:04:CD:8E`
- **Play Store**: `3C:ED:A5:0A:38:31:26:28:BD:C1:AF:26:B8:5E:C2:84:DA:66:B4:67`

### Client IDs المستخدمة:
- **Debug**: `545165014521-daav9q8d64aanct315ssfumat0fm5hir.apps.googleusercontent.com`
- **Release/Play Store**: `545165014521-tk9u5kbu2sva7i4mni5g7h8bc2k5o6ej.apps.googleusercontent.com`
- **Web**: `545165014521-j89kvjdhljgjkpi491km7qdu7rkgj4or.apps.googleusercontent.com`
- **iOS**: `545165014521-6lga42ejvda9e36l607gkllobevvpjfn.apps.googleusercontent.com`

## كود Flutter
الكود يستخدم الإعداد الافتراضي من `google-services.json` وهذا صحيح:

```dart
final GoogleSignIn googleSignIn = GoogleSignIn(
  scopes: ['email', 'profile'],
  signInOption: SignInOption.standard,
);
```

## خطوات الاختبار
1. بناء APK للإنتاج: `flutter build apk --release`
2. تثبيت APK واختبار Google Sign-In
3. إذا نجح في APK، فسيعمل في Play Store أيضاً

## SHA-1 Fingerprints Used

### Debug Keystore
- **SHA-1**: `8A:C7:3C:0C:FF:C2:1C:28:03:84:97:FB:8E:33:F3:7B:1D:55:D5:23`
- **Client ID**: `545165014521-daav9q8d64aanct315ssfumat0fm5hir.apps.googleusercontent.com`

### Release/Play Store Keystore
- **SHA-1**: `A8:1E:FC:57:F5:B0:31:19:96:39:60:40:BD:FE:A3:7C:CD:04:CD:8E`
- **Client ID**: `545165014521-tk9u5kbu2sva7i4mni5g7h8bc2k5o6ej.apps.googleusercontent.com`
- **Updated**: Fixed SHA-1 fingerprint to match actual release keystore

## ملاحظات
- تم توحيد استخدام Client ID الصحيح للـ Play Store في جميع الملفات
- الكود يعتمد على `google-services.json` لاختيار Client ID المناسب تلقائياً
- تم الحفاظ على Client IDs الأخرى للـ Debug و iOS و Web
- **تم تصحيح SHA-1 fingerprint للـ Play Store ليطابق الـ release keystore الفعلي**

## 🔄 التحديث الإضافي - إضافة App Signing Key (2025-07-01)

### سبب المشكلة:
عندما تغيرت الـ keystore سابقاً، أصبح هناك مفتاحان مختلفان:
- **Upload Key SHA-1**: `a81efc57f5b0311996396040bdfea37ccd04cd8e` (الذي تستخدمه للرفع)
- **App Signing Key SHA-1**: `3ceda50a3b31262b0dc1af26b85ec284da66b467` (الذي يستخدمه Google Play للتوزيع)

### الحل المطبق:
تم إضافة كلا المفتاحين في google-services.json:
1. Upload Key للتطوير والاختبار المحلي
2. App Signing Key للتوزيع عبر Google Play Store

### نتائج الاختبار النهائي:
- ✅ Release Mode يعمل بشكل صحيح
- ✅ Google Sign-In يعمل مع كلا المفتاحين
- ✅ التطبيق جاهز للنشر على Play Store

## 🔧 إصلاح مشكلة اختيار الحساب (2025-07-01)

تم حل مشكلة عدم إظهار قائمة الحسابات بعد تسجيل الخروج:

### المشكلة:
- بعد تسجيل الخروج، عند الضغط على Google Sign-In مرة أخرى لا يظهر اختيار الحساب
- يدخل تلقائياً بالحساب السابق

### الحل:
1. **في login_screen.dart**: إضافة `await googleSignIn.signOut()` قبل `signIn()` لإجبار إظهار قائمة الحسابات
2. **في logout_cubit.dart**: إضافة Google Sign-Out عند تسجيل الخروج لضمان الخروج الكامل

### الملفات المحدثة:
- `lib/views/screens/login_screen/login_screen.dart`
- `lib/bloc/logout_cubit/logout_cubit.dart`

**جميع المشاكل تم حلها نهائياً! 🎉**
