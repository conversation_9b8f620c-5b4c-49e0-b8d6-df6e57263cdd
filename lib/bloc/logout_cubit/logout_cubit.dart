import 'package:bus/bloc/logout_cubit/logout_states.dart';
import 'package:bus/config/global_variable.dart';
import 'package:bus/helper/cache_helper.dart';
import 'package:bus/helper/data_state.dart';
import 'package:bus/helper/response_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_sign_in/google_sign_in.dart';

import '../../data/models/change_password_model.dart';
import '../../data/repo/auth_repo/logout_repo.dart';

class LogoutCubit extends Cubit<LogoutStates> {
  final _logoutRepo = LogoutRepo();
  LogoutCubit() : super(const LogoutStates());

  Future<void> logout() async {
    emit(state.copyWith(rStates: ResponseState.loading));

    try {
      // Check if user was signed in with Google
      final wasGoogleSignIn = CacheHelper.getBool("isGoogleSignIn") ?? false;
      debugPrint("Logout - wasGoogleSignIn: $wasGoogleSignIn");

      // If user was signed in with Google, sign out from Google
      if (wasGoogleSignIn) {
        final GoogleSignIn googleSignIn = GoogleSignIn();
        await googleSignIn.signOut();
        debugPrint("Logout - Google Sign-In signOut completed");
      }

      // Call backend logout API
      DataState<ChangePasswordModel> response = await _logoutRepo.repo();

      if (response is DataSuccess) {
        // Reset Google sign-in flag
        isGoogleSignIn = false;
        CacheHelper.putBool("isGoogleSignIn", false);
        debugPrint("Logout - Reset isGoogleSignIn to false");

        emit(state.copyWith(
            rStates: ResponseState.success, changePasswordModels: response.data));
      } else {
        emit(state.copyWith(rStates: ResponseState.failure));
      }
    } catch (error) {
      debugPrint("Logout error: $error");
      emit(state.copyWith(rStates: ResponseState.failure));
    }
  }
}
