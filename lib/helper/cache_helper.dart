import 'package:shared_preferences/shared_preferences.dart';

class CacheHelper {
  static final CacheHelper _cacheHelper = CacheHelper._();
  factory CacheHelper() {
    init();
    return _cacheHelper;
  }
  CacheHelper._();

  static SharedPreferences? _sp;
  static Future<void> init() async {
    if (_sp == null) {
      print("CACHE DEBUG: Initializing SharedPreferences");
      _sp = await SharedPreferences.getInstance();
      print("CACHE DEBUG: SharedPreferences initialized");

      // Check if auth_method is already set
      final authMethod = _sp!.getString("auth_method");
      print("CACHE DEBUG: Initial auth_method value: $authMethod");
    } else {
      print("CACHE DEBUG: SharedPreferences already initialized");
    }
  }

  static Future<bool> putString(String key, String value) async {
    // Add debug logging to track cache storage
    if (key == "auth_method") {
      print("CACHE DEBUG: Setting auth_method in cache: $value");

      // FIXED: Direct set without removing first - this was causing the issue
      final result = await _sp!.setString(key, value);

      // Verify the value was set correctly
      final storedValue = _sp!.getString(key);
      print("CACHE DEBUG: After setting, auth_method in cache: $storedValue");

      if (storedValue != value) {
        print(
            "CACHE DEBUG: ERROR - Value mismatch! Expected: $value, Got: $storedValue");
        // Try one more time with a small delay to ensure persistence
        await Future.delayed(Duration(milliseconds: 10));
        await _sp!.setString(key, value);
        final finalValue = _sp!.getString(key);
        print("CACHE DEBUG: After retry, auth_method in cache: $finalValue");
        return finalValue == value;
      }

      return result;
    } else {
      // Normal behavior for other keys
      return await _sp!.setString(key, value);
    }
  }

  static Future<bool> putInt(String key, int value) async {
    return await _sp!.setInt(key, value);
  }

  static Future<bool> putBool(String key, dynamic value) async {
    return await _sp!.setBool(key, value);
  }

  static Future<bool> putListOfString(String key, List<String> value) async {
    return await _sp!.setStringList(key, value);
  }

  // static Future<bool> pusModals(String key, ProfileModals value) async {
  //   return await _sp!.setString(key, jsonEncode(ProfileModals));
  // }

  static List<String>? getListOfString(String key) {
    return _sp!.getStringList(key);
  }

  static bool? getBool(String key) {
    return _sp!.getBool(key);
  }

  static int? getInt(String key) {
    return _sp!.getInt(key);
  }

  static String? getString(String key) {
    // Add debug logging to track cache retrieval
    final value = _sp!.getString(key);
    if (key == "auth_method") {
      print("CACHE DEBUG: Getting auth_method from cache: $value");
    }
    return value;
  }

  static Future<bool> remove(String key) async {
    if (key == "auth_method") {
      print("CACHE DEBUG: Removing auth_method from cache");
      print("CACHE DEBUG: auth_method before removal: ${_sp!.getString(key)}");
    }

    final result = await _sp!.remove(key);

    if (key == "auth_method") {
      print("CACHE DEBUG: auth_method after removal: ${_sp!.getString(key)}");
    }

    return result;
  }

  static Future<bool> clearAllSaved() async {
    print("CACHE DEBUG: Clearing all saved data");
    // Check auth_method before clearing
    final authMethodBeforeClear = _sp!.getString("auth_method");
    print("CACHE DEBUG: auth_method before clearing: $authMethodBeforeClear");

    final result = await _sp!.clear();

    // Check auth_method after clearing
    final authMethodAfterClear = _sp!.getString("auth_method");
    print("CACHE DEBUG: auth_method after clearing: $authMethodAfterClear");

    return result;
  }

  /// Get all keys from SharedPreferences
  static Set<String> getKeys() {
    return _sp?.getKeys() ?? <String>{};
  }
}
